use std::{collections::HashMap, sync::Arc};

use alloy::primitives::{utils::{format_units, parse_units}, Address, U256};
use colored::Colorize;
use futures::StreamExt;
use std::pin::Pin;
use futures::Stream;

use crate::{tools::now_str, vira::{pool::DexPool, status::{cache::CacheArbitrage, mev::{self, Mev, MevPool, Order}}, Vira}, CONFIG};
use eyre::Result;

#[derive(Debug, Clone)]
pub struct FindPathResult_old {
    pub stable: Address,           // 稳定币地址
    pub convert_eth: bool,         // 是否需要转换ETH
    pub mev_type: u8,             // MEV类型，对应calc字段

    pub gas_cost: U256,           // gas消耗等值的stable数量
    pub pools: Vec<MevPool>,      // 池子地址数组
    pub gas: U256,                // 消耗的gas
    
    // 用于统计
    pub amount: U256,             // 输入金额
    pub reward: U256,              // 套利token的数量
    pub reward_value: f32,        // 套利token的价值(USD)

    // 用于计算
    pub amounts: Vec<U256>,       // 每一步的金额
}

#[derive(Debug, Clone)]
pub struct FindPathResult {
    pub mev : Mev,
    pub rewards : AmountCalculationResult,
    pub order : Order,
}

/// 计算结果结构体
#[derive(Debug, Clone)]
pub struct AmountCalculationResult {
    pub amounts: Vec<U256>,       // 每一步的金额
    pub token: U256,              // 套利token的数量
    pub token_usd: f32,         // 套利token的价值(usd)
}


pub struct Trash {
    pub vira : Arc<Vira>,
}

impl Trash {
    pub fn new(vira : Arc<Vira>) -> Trash {
        Trash { vira }
    }

    pub async fn listen(&mut self, mut updated_pools_stream: Pin<Box<dyn Stream<Item = Vec<Address>> + Send>>) -> Result<()> {
        println!("{} {}", now_str(), "Trash listening...".green());
        while let Some(updated_pools) = updated_pools_stream.next().await {
            // 处理更新
            self.on_updated_pools(updated_pools);
        }
        
        println!("{} {}", now_str(), "Trash stream closed, stopping...".red());
        Ok(())
    }

    fn on_updated_pools(&self, updated_pools : Vec<Address>) {
        let mut cache = CacheArbitrage::new();
        let mut mevs = vec![];

        for (i, addr) in updated_pools.iter().enumerate() {
            //打印有变动的POOL
            let p = &*self.vira.sm.pools.data.get(addr).unwrap();
            let mut str = format!("    {}. {}, ", i, p.addr());
            p.data().tokens.iter().for_each(|t| {
                let reserve = format_units(t.reserve, t.decimal).unwrap();
                let reserve = format!("{:.2}", reserve.parse::<f64>().unwrap());
                str += format!("{} ({}) ", t.symbol, reserve).as_ref();
            });
            println!("{}", str.black());
            if i > 6 {
                println!("       ......");
                break;
            }
            if let Some(mev) = self.find_path(*addr, Order::Asc, U256::ZERO, &mut cache, None){
                mevs.extend(mev);
            }
            if let Some(mev) = self.find_path(*addr, Order::Desc, U256::ZERO, &mut cache, None){
                mevs.extend(mev);
            }
        }

        if mevs.len() > 0 {
            //TODO: 
        }
    }

    /// 寻找套利路径的主函数
    ///
    /// 优化特点：
    /// 1. 返回 None 当无法找到有效的 MEV 套利路径时
    /// 2. 返回 None 当找到的路径没有利润时
    /// 3. 使用黄金分割法进行 optimal_amount 计算优化
    /// 4. 实现懒加载缓存策略，减少内存分配
    fn find_path(
        &self,
        pair_addr: Address,
        order: Order,
        gas_cost: U256,
        cache: &mut CacheArbitrage,
        path_num: Option<usize>
    ) -> Option<Vec<FindPathResult>> {
        // 获取MEV路径，如果没有则返回None
        let mev_paths = match self.vira.sm.pools.mevs.get(&pair_addr) {
            Some(paths) => paths.clone(),
            None => return None, // 没有MEV路径直接返回None
        };

        if mev_paths.is_empty() {
            return None; // 空路径返回None
        }

        let mut results = Vec::new();
        let max_paths = path_num.unwrap_or(6);

        // 遍历每个MEV路径寻找有利可图的套利机会
        for (i, mev_path) in mev_paths.iter().enumerate() {
            if i >= max_paths { break; }

            // 根据交易方向确定稳定币token
            let stable_token = if order == Order::Asc {
                mev_path.s_in
            } else {
                mev_path.s_out
            };

            // 计算最优金额 - 使用不同的优化算法
            let optimal_amount = match mev_path.calc {
                0 => {
                    // 使用V2优化算法（适用于V1和V2池子）
                    mev::find_optimal_amount_v2(self.vira.sm.pools.clone(), cache, &mev_path, &order)
                },
                _ => {
                    // 使用黄金分割法进行优化计算
                    self.calculate_optimal_amount_with_golden_section(&stable_token, mev_path, &order, cache)
                }
            };

            // 验证最优金额是否有效
            let optimal_amount_value = match optimal_amount {
                Some(amount) if !amount.is_zero() => amount,
                _ => continue, // 没有找到有利可图的金额，跳过此路径
            };

            // 计算交易结果并更新缓存（懒加载策略）
            let calc_result = self.get_amounts_out_and_update_cache(
                optimal_amount_value,
                mev_path,
                gas_cost,
                cache,
                &order
            );

            // 如果没有利润，直接跳过这个路径
            let calc_result = match calc_result {
                Some(result) => result,
                None => continue, // 无利润，跳过
            };

            // 创建FindPathResult结果

            let find_path_result = FindPathResult {
                mev : mev_path.clone(),
                rewards : calc_result,
                order : order.clone(),
            };

            results.push(find_path_result);
        }

        // 如果没有找到任何有利可图的路径，返回None
        if results.is_empty() {
            None
        } else {
            Some(results)
        }
    }


    /// 使用黄金分割法计算最优金额（重构优化版本）
    ///
    /// 重构优化特点：
    /// 1. 根据稳定币token从golden_cfg获取搜索范围
    /// 2. 使用mev.rs中的通用零克隆黄金分割法实现
    /// 3. 直接调用重构后的函数，避免生命周期问题
    /// 4. 完全消除重复代码，提高代码复用性
    /// 5. 保持原有的性能优化和功能完整性
    fn calculate_optimal_amount_with_golden_section(
        &self,
        stable_token: &Address,
        mev_path: &Mev,
        order: &Order,
        cache: &CacheArbitrage
    ) -> Option<U256> {
        // 从golden_cfg获取搜索范围
        let (min_amount, max_amount) = match CONFIG.golden.get(stable_token) {
            Some(&(min, max)) => (min, max),
            None => {
                // 如果没有配置，使用默认范围
                panic!("no golden cfg for token: {:?}", stable_token);
            }
        };

        // 使用重构后的零克隆黄金分割法实现
        self.find_max_golden(min_amount, max_amount, mev_path, cache, order)
    }

    /// 重构后的零克隆黄金分割法实现（调用mev.rs中的通用函数）
    ///
    /// 这个函数作为适配器，将trash.rs特定的缓存逻辑与mev.rs中的通用算法结合
    #[inline(always)]
    fn find_max_golden(
        &self,
        amount_min: U256,
        amount_max: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        order: &Order
    ) -> Option<U256> {
        use crate::vira::consts::{MAX_GOLDEN_STEPS, U256_618, U256_1000, UINT112_MAX, U_2};

        let mut step = 0u8;

        // 提前检查最小值是否有利润
        let mut amount_out1 = self.get_amount_out_by_pool(amount_min, mev_path, cache, order)?;
        if amount_out1 < amount_min {
            return None;
        }

        // 初始化搜索区间
        let mut low = amount_min;
        let mut high = amount_max;

        // 使用黄金分割比例 1.618034
        let mut x1 = high - ((high - low) * U256_618 / U256_1000);
        let mut x2 = low + ((high - low) * U256_618 / U256_1000);

        // 预先计算初始点的输出值
        let mut amount_out2 = self.get_amount_out_by_pool(x2, mev_path, cache, order)?;

        // 使用 max 来避免溢出
        let mut f1 = amount_out1 + UINT112_MAX - x1;
        let mut f2 = amount_out2 + UINT112_MAX - x2;

        // 优化主循环
        while high > low + amount_min && step < MAX_GOLDEN_STEPS {
            if f1 > f2 {
                high = x2;
                x2 = x1;
                f2 = f1;
                x1 = high - ((high - low) * U256_618 / U256_1000);
                amount_out1 = self.get_amount_out_by_pool(x1, mev_path, cache, order)?;
                f1 = amount_out1 + UINT112_MAX - x1;
            } else if f1 < f2 {
                low = x1;
                x1 = x2;
                f1 = f2;
                x2 = low + ((high - low) * U256_618 / U256_1000);
                amount_out2 = self.get_amount_out_by_pool(x2, mev_path, cache, order)?;
                f2 = amount_out2 + UINT112_MAX - x2;
            } else {
                break;
            }
            step += 1;
        }

        // 返回结果
        let result = (high + low) / U_2;
        if result.is_zero() {
            None
        } else {
            Some(result)
        }
    }

    /// 重构后的零克隆池子交换计算（调用mev.rs中的通用函数）
    ///
    /// 这个函数作为适配器，将trash.rs特定的缓存逻辑与mev.rs中的通用算法结合
    #[inline(always)]
    fn get_amount_out_by_pool(
        &self,
        initial_input_amount: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        swap_direction: &Order
    ) -> Option<U256> {
        let mut current_amount = initial_input_amount;
        let pool_count = mev_path.pools.len();

        // 根据交换方向确定处理顺序
        let pool_indices: Vec<usize> = match swap_direction {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        // 遍历每个池子进行交换模拟，使用mev.rs中的通用函数
        for &pool_index in &pool_indices {
            let pool_info = &mev_path.pools[pool_index];

            // 分别处理缓存和主存储的引用，调用mev.rs中的通用函数
            current_amount = if let Some(cached_pool_ref) = cache.data.get(&pool_info.addr) {
                // 缓存命中：使用DashMap的引用
                mev::simulate_swap_with_pool_ref(&*cached_pool_ref, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else if let Some(main_pool_ref) = self.vira.sm.pools.data.get(&pool_info.addr) {
                // 主存储命中：使用HashMap的引用
                mev::simulate_swap_with_pool_ref(&main_pool_ref, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else {
                // 找不到池子，无法继续计算
                return None;
            };
        }

        Some(current_amount)
    }



    /// 使用黄金分割法计算最优金额（保留原有方法以保持兼容性）
    #[allow(dead_code)]
    fn calculate_golden_amount(
        &self,
        min_amount: U256,
        max_amount: U256,
        mev_path: &Mev,
        order: Order,
        cache: &CacheArbitrage
    ) -> U256 {
        // 创建池子引用向量
        let mut pool_refs = Vec::new();

        for pool_info in &mev_path.pools {
            if let Some(pool) = cache.data.get(&pool_info.addr) {
                pool_refs.push(pool.clone());
            } else if let Some(pool) = self.vira.sm.pools.data.get(&pool_info.addr) {
                pool_refs.push(pool.clone());
            } else {
                return U256::ZERO; // 找不到池子
            }
        }

        // 创建引用向量
        let pool_refs_slice: Vec<crate::vira::pool::POOL> = pool_refs.into_iter().collect();

        mev::find_max_golden(min_amount, max_amount, mev_path, &pool_refs_slice, order)
    }

    /// 零克隆优化版本：计算交易输出并更新缓存
    ///
    /// 终极优化特点：
    /// 1. 完全消除池子克隆操作，使用自定义计算函数直接处理引用
    /// 2. 实现懒加载缓存策略，只有确认有利润时才执行昂贵操作
    /// 3. 无利润时返回 None，避免无效计算结果的传递
    /// 4. 通过内联计算避免生命周期问题
    /// 5. 保持与 mev.rs 代码模式的兼容性
    fn get_amounts_out_and_update_cache(
        &self,
        amount_in: U256,
        mev_path: &Mev,
        gas_cost: U256,
        cache: &mut CacheArbitrage,
        order: &Order
    ) -> Option<AmountCalculationResult> {
        // 获取输入token用于利润计算
        let token_in = match order {
            Order::Asc => mev_path.s_in,
            Order::Desc => mev_path.s_out,
        };

        // 零克隆策略：直接使用引用进行计算，避免创建临时向量
        let mut uncached_addrs = Vec::new(); // 记录需要稍后缓存的池子地址

        // 使用自定义零克隆计算函数，直接处理引用
        let amount_outs = self.calculate_amounts(
            amount_in,
            mev_path,
            cache,
            order,
            &mut uncached_addrs
        )?; // 如果计算失败（找不到池子），直接返回 None

        let amount_out = amount_outs.last().unwrap_or(&U256::ZERO);

        // 早期退出：如果输出金额不大于输入金额，直接返回 None
        if amount_out <= &amount_in {
            return None;
        }

        // 计算利润
        let profit_amount = amount_out.saturating_sub(amount_in).saturating_sub(gas_cost);
        if profit_amount.is_zero() {
            return None;
        }
        let profit_value = self.vira.sm.tokens.get(&token_in).usd(profit_amount);

        // 最小利润要求 $0.005
        if profit_value < 0.005 {
            return None;
        }

        // 懒加载缓存策略：只有在确认有利润后，才将未缓存的池子添加到缓存
        // 这里需要重新查找并克隆，但只对有利润的路径执行
        for addr in uncached_addrs {
            if let Some(main_pool) = self.vira.sm.pools.data.get(&addr) {
                cache.data.insert(addr, main_pool.clone());
            }
        }

        // 更新缓存中池子的储备金状态
        self.update_cache_reserves(cache, mev_path, &amount_outs, order);

        Some(AmountCalculationResult {
            amounts: amount_outs,
            token: profit_amount,
            token_usd: profit_value
        })
    }

    /// 零克隆计算函数：通过逐个处理池子避免生命周期问题
    ///
    /// 这个函数通过逐个处理池子，在每次循环中独立处理引用，
    /// 避免了跨循环的生命周期问题，实现接近零克隆的性能
    ///
    /// 参数：
    /// - uncached_addrs: 用于记录需要稍后缓存的池子地址
    ///
    /// 返回：
    /// - Option<Vec<U256>>: 计算成功返回金额数组，失败返回 None
    #[inline(always)]
    fn calculate_amounts(
        &self,
        initial_input_amount: U256,
        mev_path: &Mev,
        cache: &CacheArbitrage,
        swap_direction: &Order,
        uncached_addrs: &mut Vec<Address>
    ) -> Option<Vec<U256>> {
        let mut amounts = Vec::with_capacity(mev_path.pools.len() + 1);
        amounts.push(initial_input_amount); // 第一个元素是输入金额

        let mut current_amount = initial_input_amount;
        let pool_count = mev_path.pools.len();

        // 根据交换方向确定处理顺序
        let pool_indices: Vec<usize> = match swap_direction {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        // 遍历每个池子进行交换模拟，每次循环独立处理引用
        for &pool_index in &pool_indices {
            let pool_info = &mev_path.pools[pool_index];

            // 在每次循环中独立处理池子引用，避免生命周期问题
            let amount_out = if let Some(cached_pool) = cache.data.get(&pool_info.addr) {
                // 缓存命中：直接使用引用进行计算
                self.simulate_swap_with_pool(cached_pool, pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else if let Some(main_pool_ref) = self.vira.sm.pools.data.get(&pool_info.addr) {
                // 主存储命中：使用引用进行计算，记录地址稍后缓存
                uncached_addrs.push(pool_info.addr);
                self.simulate_swap_with_pool(main_pool_ref.value(), pool_info, mev_path, pool_index, current_amount, swap_direction)?
            } else {
                // 找不到池子，返回 None
                return None;
            };

            current_amount = amount_out;
            amounts.push(amount_out);
        }

        Some(amounts)
    }

    /// 使用池子引用执行交换模拟
    ///
    /// 这个辅助函数封装了交换模拟逻辑，避免代码重复
    ///
    /// 返回：
    /// - Option<U256>: 交换成功返回输出金额，失败返回 None
    #[inline(always)]
    fn simulate_swap_with_pool(
        &self,
        pool_ref: &crate::vira::pool::POOL,
        pool_info: &crate::vira::status::mev::MevPool,
        mev_path: &Mev,
        pool_index: usize,
        current_amount: U256,
        swap_direction: &Order
    ) -> Option<U256> {
        let pool_data = pool_ref.data();

        // 根据交换方向确定费用、输入和输出token
        let (custom_fee, token_in, token_out) = match swap_direction {
            Order::Asc => (
                pool_info.fee1,
                pool_data.tokens[pool_info.in_index].addr,
                pool_data.tokens[pool_info.out_index].addr,
            ),
            Order::Desc => (
                pool_info.fee0,
                pool_data.tokens[pool_info.out_index].addr,
                pool_data.tokens[pool_info.in_index].addr,
            ),
        };

        // 执行交换模拟
        match pool_ref.simulate_swap(token_in, token_out, current_amount, Some(custom_fee)) {
            Ok(amount_out) => Some(amount_out),
            Err(_) => None, // 交换失败
        }
    }

    /// 更新缓存中池子的储备金状态
    ///
    /// 根据交易结果更新缓存中每个池子的储备金状态
    ///
    /// 参数：
    /// - cache: 套利缓存对象
    /// - mev_path: MEV 路径信息
    /// - amount_outs: 每一步交换的输出金额数组（第一个元素是输入金额）
    /// - order: 交换方向
    fn update_cache_reserves(
        &self,
        cache: &mut CacheArbitrage,
        mev_path: &Mev,
        amount_outs: &[U256],
        order: &Order,
    ) {
        // 根据交换方向确定处理顺序
        let pool_count = mev_path.pools.len();
        let pool_indices: Vec<usize> = match order {
            Order::Asc => (0..pool_count).collect(),
            Order::Desc => (0..pool_count).rev().collect(),
        };

        // 遍历每个池子，按交换顺序更新储备金
        for (step_index, &pool_index) in pool_indices.iter().enumerate() {
            let pool_info = &mev_path.pools[pool_index];

            // 获取缓存中的池子进行状态更新
            if let Some(cached_pool) = cache.data.get_mut(&pool_info.addr) {
                // 获取当前步骤的输入和输出金额
                let amount_in = amount_outs[step_index];     // 当前步骤的输入金额
                let amount_out = amount_outs[step_index + 1]; // 当前步骤的输出金额

                // 根据交换方向确定输入输出token的索引
                let (in_index, out_index) = match order {
                    Order::Asc => (pool_info.in_index, pool_info.out_index),
                    Order::Desc => (pool_info.out_index, pool_info.in_index),
                };

                // 更新池子的储备金状态
                // 注意：这里直接修改缓存中的池子，不影响原始数据
                cached_pool.update_reserve_by_index(in_index, out_index, amount_in, amount_out);
            }
        }
    }

}

// 包含性能测试模块
#[cfg(test)]
mod tests {
}


