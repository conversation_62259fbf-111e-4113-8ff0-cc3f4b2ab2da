use std::{collections::HashMap, str::FromStr, sync::Arc};
use alloy::{primitives::{utils, Address, U256}, sol};
use crate::{connector::Connector, vira::consts::U_900005, CONFIG};
use super::{consts::{BATCH_SIZE, UPDATE_BALANCE_RETRY}, errors};
use tokio::sync::RwLock;

sol! (
    #[derive(Debug)]
    #[allow(missing_docs)]
    #[sol(rpc)]
    IVira,
    "src/vira/contract/ViraLogic.json"
);

sol! {
    #[derive(Debug)]
    #[sol(rpc)]
    interface IERC20 {
        function balanceOf(address account) external view returns (uint256);
    }
}

pub struct Contract {
    pub owner : Address,
    pub addr : Address,
    pub connector : Arc<Connector>,

    pub balance : RwLock<HashMap<Address, U256>>,
}

impl Contract {

    pub fn new(address : Address, connector : Arc<Connector>) -> Contract {
        Contract {
            owner : Address::from_str("0xF109A1D7f1bDD87F7251637D27D8c30DA7E07e7F").unwrap(),
            addr : address,
            connector : connector,
            balance : RwLock::new(HashMap::new())
        }
    }

    pub async fn update_balance(&mut self) -> Result<(), Box<dyn std::error::Error>> {
        // 遍历所有稳定币地址
        for &stable_addr in CONFIG.stables.iter() {
            // 创建ERC20合约实例
            let token = IERC20::new(stable_addr, self.connector.provider());
            // 调用合约获取余额
            // Retry the balance fetching in case of an error
            let mut attempts = 0;
            let mut success = false;

            while attempts < UPDATE_BALANCE_RETRY && !success {
                attempts += 1;
                match token.balanceOf(self.addr).call().await {
                    Ok(result) => {
                        let mut balance = self.balance.write().await;
                        balance.insert(stable_addr, result);
                        if let Some(t) = CONFIG.tokens.get(&stable_addr) {
                            println!("{}: {}", t.name, utils::format_units(result, t.decimals).unwrap());
                        }
                        success = true;
                    },
                    Err(e) => {
                        println!("Attempt {}: Failed to get balance for stable token {}: {:?}", attempts, stable_addr, e);
                        if attempts == UPDATE_BALANCE_RETRY {
                            println!("Exceeded maximum retry attempts for stable token {}", stable_addr);
                            panic!("update balance error");
                        } else {
                            // Wait before retrying
                            tokio::time::sleep(tokio::time::Duration::from_secs(3)).await;
                        }
                    }
                }
            }
        }
        Ok(())
    }

    pub async fn get_balance(&self) -> HashMap<Address, U256> {
        let balance = self.balance.read().await;
        balance.clone()
    }

    pub async fn get_balance_by_addr(&self, addr: &Address) -> Option<U256> {
        let balance = self.balance.read().await;
        balance.get(addr).copied()
    }

    // 提取获取stable_addrs和stble_amounts的共同逻辑到一个私有方法中
    async fn get_stable_addrs_and_amounts(&self, split: Option<usize>) -> (Vec<Address>, Vec<U256>) {
        let mut balances = self.get_balance().await;
        let split = split.unwrap_or(1);

        //把balances所有的值除以BATCH_SIZE, 保证check期间不会出现stable为0的情况
        for v in balances.values_mut() {
            *v /= U256::from(split);
        }
        //检查balances是否有值为0的情况
        for v in balances.values() {
            if *v == U256::ZERO {
                panic!("balances has zero value");
            }
        }
        
        // 把balances的keys和values分别生成两个新的vec!
        let stable_addrs: Vec<_> = balances.keys().cloned().collect();
        let stable_amounts: Vec<_> = balances.values().cloned().collect();

        (stable_addrs, stable_amounts)
    }

    pub async fn batch_check_pair_fee(&self, inputs : Vec<ViraLogic::CheckPairFeeInputDesc>) -> Result<Vec<Vec<U256>>, errors::DEXError>{
        let contract = IVira::new(self.addr, self.connector.provider());
        let batch_size = inputs.len();
        let (stable_addrs, stable_amounts) = self.get_stable_addrs_and_amounts(Some(batch_size)).await;

        // 先尝试批量请求
        match contract.batchCheckPairFee(inputs.clone(), stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
            Ok(res) => Ok(res),
            Err(e) => {
                println!("Batch request failed: {:?}, retrying with individual requests", e);
                //停顿2秒
                tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;
                // 批量请求失败，改为逐个请求
                let mut all_fees = Vec::new();
                for input in inputs {
                    let addr = &input.pair[0].addr.clone();
                    match contract.batchCheckPairFee(vec![input], stable_addrs.clone(), stable_amounts.clone()).from(self.owner).call().await {
                        Ok(res) => all_fees.extend(res),
                        Err(e) => {
                            println!("Individual request failed: {}, {:?}", addr, e);
                            // - 900005: 未知原因
                            all_fees.push(vec![U_900005]);
                        }
                    }
                }
                Ok(all_fees)
            }
        }
    }
    
    pub async fn batch_check_mev(&self, inputs : Vec<Vec<ViraData::PoolReq>>) -> Vec<ViraLogic::CheckMevsResultDesc>{
        let batch_size = inputs.len();
        let contract = IVira::new(self.addr, self.connector.provider());
        let (stable_addrs, stable_amounts) = self.get_stable_addrs_and_amounts(Some(batch_size)).await;
        let res = contract.batchCheckMev(inputs, stable_addrs, stable_amounts).call().await.unwrap();
        res
    }
}

mod tests {
    use crate::{config, vira::Vira};

    use super::*;

    #[tokio::test]
    async fn test_update_balance() {
        let mut vira = Vira::new().await;
        let _ = vira.contract.update_balance().await;
    }
}