//! 同步状态
//! 
//! 该模块负责同步状态，包括检查池子的fee和同步池子
//! 
//! 主要函数:
//! - sync: 同步所有pool的数据
//! - check_pools_fee: 检查池子的fee
//! - process_fee_results: 处理fee结果
//! - process_non_stable_pools: 处理非稳定币池子

use std::sync::Arc;

use alloy::primitives::{Address, U256};
use alloy::{eips::BlockId, providers::Provider};
use futures::stream::{BufferUnordered, StreamExt};
use futures::stream::iter;
use colored::Colorize;
use std::collections::HashSet;
use std::sync::atomic::{AtomicUsize, Ordering};

use crate::vira::status::mev::MevStatus;
use crate::vira::status::pools::Pools;
use crate::CONFIG;
use crate::{connector::Connector, vira::{consts::{BATCH_SIZE, U_2000, U_666666, U_900004, U_900005, U_ZERO, MEV_BATCH_SIZE}, contract::{Contract, ViraData, ViraLogic}, dex::{factory::{DexFactory, FACTORY}}, errors::DEXError, pool::{DexPool, Status, POOL}, status::mev::Mev}};

// 类型别名，简化使用
type PoolReq = ViraData::PoolReq;

use super::StatusManager;

// 并发限制常量
const MAX_CONCURRENT_TASKS: usize = 20;


//核心逻辑: 同步最新的pool addr并且更新所需data
pub async fn sync_new_pools(sm :&mut StatusManager, connector : Arc<Connector>) -> Result<(), DEXError> {
    let chain_tip = BlockId::from(connector.provider.get_block_number().await?);
    let factories = sm.factories.clone();
    
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(factories.into_iter().map(|(_, mut factory)| {
        let co = connector.clone();
        async move {
            let mut discovered_amms = factory.discover(chain_tip, co.clone()).await?;

            discovered_amms = factory.sync(discovered_amms, chain_tip, co).await?;

            Ok::<(Vec<POOL>, FACTORY), DEXError>((discovered_amms, factory))
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);

    let mut new_pools = Vec::new();
    futures::pin_mut!(futures);
    while let Some(res) = futures.next().await {
        let (synced_amms, factory) = res?;
        new_pools.extend(synced_amms);
        sm.factories.insert(factory.address(), factory); //更新factory的索引到当前更新到的位置
    }

    check_pools_fee(new_pools, sm, connector.clone()).await?;

    Ok(())
}


/// 检查池子的fee
/// 
/// 该函数接收一个池子列表、状态管理器和连接器，
/// 并对每个池子进行费用检查。检查的结果将更新到pools并且添加到sm中
/// 
/// 参数:
/// - `pools`: 需要检查费用的池子列表
/// - `sm`: 状态管理器，用于管理池子的状态
/// - `connector`: 连接器，用于与区块链进行交互
/// 
/// 返回:
/// - `Result<(), DEXError>`: 成功时返回 Ok，失败时返回 DEXError
/// 
/// 核心逻辑:
/// 1. 遍历pools，把包含stable的pools分成一组，每BATCH_SIZE数量的pools使用contract.batch_check_pair_fee检查fee, 根据返回的结果设置pool的状态
///    - 默认状态为Status::Good
///         1) fee == 900004 (通缩币) Status::OddBad;
///         2) fee == 900005 (未知状态) Status::Unknown;
///         3) fee > 2000 Status::Bad;
///         4) 0 < fee < 2000 Status::Fee;
///         5) fee == 666666 (蜜罐币) Status::OddGood;
///         6) 其余情况保持Good状态
/// 2. 把OddGood和Good的pool增加到sm中
/// 3. 检查剩余的uncheck pool, 逻辑参考 checkpoint::process_non_stable_pools
/// 4. 把剩余pools中状态为OddGood和Good的pool增加到sm中
/// 5. 重复一次步骤3和4
/// 6. 动态打印结果

pub async fn check_pools_fee(pools: Vec<POOL>, sm: &mut StatusManager, connector: Arc<Connector>) -> Result<(), DEXError> {
    println!("{}", "\nChecking pools fee...".green());

    // 创建合约实例
    let contract = Arc::new(Contract::new(CONFIG.contract, connector.clone()));

    // 统计总池子数量
    let total_pools = pools.len();
    println!("Total pools to check: {}", total_pools);

    // 将池子分成两组：包含稳定币和不包含稳定币的
    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    
    let (stable_pools, non_stable_pools): (Vec<_>, Vec<_>) = pools
        .into_iter()
        .partition(|pool| {
            pool.data().tokens.iter().any(|token| {
                stables.contains(&token.addr)
            })
        });
    
    let stable_len = stable_pools.len();
    let non_stable_len = non_stable_pools.len();
    
    println!("Stable pools: {}, Non-stable pools: {}", 
        stable_len, 
        non_stable_len
    );

    // 处理所有池子的进度计数器
    let processed = Arc::new(AtomicUsize::new(0));

    // 1. 处理包含稳定币的池子
    let processed_stable_pools = process_stable_pools(stable_pools, sm, contract.clone(), processed.clone()).await?;
    
    // 2. 处理不包含稳定币的池子
    let processed_non_stable_pools = process_non_stable_pools_concurrent(
        non_stable_pools, 
        &contract, 
        sm, 
        processed.clone(),
        stable_len, // 开始索引从stable_pools.len()开始
        &stables
    ).await?;

    println!("\nPool fee checking completed! Total processed: {}", 
        processed_stable_pools + processed_non_stable_pools);
    
    Ok(())
}

/// 处理包含稳定币的池子，并发处理多个批次
async fn process_stable_pools(
    stable_pools: Vec<POOL>, 
    sm: &mut StatusManager, 
    contract: Arc<Contract>,
    processed_counter: Arc<AtomicUsize>
) -> Result<usize, DEXError> {
    if stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing stable pools...");
    let total = stable_pools.len();
    let chunks: Vec<Vec<POOL>> = stable_pools
        .chunks(BATCH_SIZE)
        .map(|chunk| chunk.to_vec())
        .collect();
    
    // 重置处理计数器确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(chunks.into_iter().enumerate().map(|(_, chunk)| {
        let contract_clone = contract.clone();
        let processed_counter_clone = processed_counter.clone();
        
        async move {
            let pool_reqs = prepare_stable_pool_requests(&chunk);
            
            if pool_reqs.is_empty() {
                return Ok::<(Vec<POOL>, Vec<Vec<alloy::primitives::U256>>), DEXError>((chunk, vec![]));
            }
            
            match contract_clone.batch_check_pair_fee(pool_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(chunk.len(), Ordering::SeqCst);
                    eprint!("\r处理稳定币池子进度: {}/{} ({:.2}%)", 
                        current + chunk.len(), 
                        total,
                        ((current + chunk.len()) as f64 / total as f64) * 100.0
                    );
                    
                    Ok((chunk, fees))
                },
                Err(e) => Err(e)
            }
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);
    
    let mut processed_pools = Vec::new();
    let mut processed_count = 0;
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((chunk, fees)) => {
                if !fees.is_empty() {
                    let mut chunk_copy = chunk.clone();
                    process_fee_results(&mut chunk_copy, &fees, sm, processed_count);
                    processed_count += chunk.len();
                    processed_pools.extend(chunk);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        total,
        (processed_count as f64 / total as f64) * 100.0
    );
    
    Ok(processed_count)
}

/// 准备稳定币池子的请求
fn prepare_stable_pool_requests(pools: &[POOL]) -> Vec<ViraLogic::CheckPairFeeInputDesc> {
    let stables: HashSet<_> = CONFIG.stables.iter().cloned().collect();
    let mut pool_reqs = Vec::with_capacity(pools.len());
    
    for pool in pools {
        let data = pool.data();
        // 找到稳定币的索引
        let stable_index = data.tokens.iter()
            .position(|t| stables.contains(&t.addr))
            .unwrap_or(0);
            
        // 构建请求
        pool_reqs.push(ViraLogic::CheckPairFeeInputDesc {
            prePair: vec![],
            pair: vec![PoolReq {
                addr: data.addr,
                version: U256::from(data.ver),
                fee: U_ZERO,
                fp: data.fp,
                inIndex: U256::from(stable_index),
                outIndex: U_ZERO,
            }],
        });
    }
    
    pool_reqs
}

/// 并发处理非稳定币池子
async fn process_non_stable_pools_concurrent(
    non_stable_pools: Vec<POOL>, 
    contract: &Arc<Contract>, 
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
    stables: &HashSet<alloy::primitives::Address>
) -> Result<usize, DEXError> {
    if non_stable_pools.is_empty() {
        return Ok(0);
    }

    println!("\nProcessing non-stable pools (1st round)...");
    
    // 第一轮处理
    let (processed_first, remaining_pools) = process_non_stable_pools_round(
        non_stable_pools,
        contract,
        sm,
        processed_counter.clone(),
        start_index,
        stables,
        false
    ).await?;
        
    if remaining_pools.is_empty() {
        return Ok(processed_first);
    }
    
    // 第二轮处理
    println!("\nProcessing non-stable pools (2nd round)...");
    let (processed_second, _) = process_non_stable_pools_round(
        remaining_pools,
        contract,
        sm,
        processed_counter,
        start_index + processed_first,
        stables,
        true
    ).await?;
    
    let final_count = processed_first + processed_second;
    // (暂时去除)标记剩余未检查的池子为NoStable, 有用的pool都已经添加到sm当中
    /*
    for mut pool in remaining_second {
        if pool.data().status == Status::UnChecked {
            pool.data_mut().status = Status::NoStable;
            final_count += 1;
        }
    }
    */
    Ok(final_count)
}

/// 并发处理非稳定币池子的单轮
async fn process_non_stable_pools_round(
    pools: Vec<POOL>,
    contract: &Arc<Contract>,
    sm: &mut StatusManager,
    processed_counter: Arc<AtomicUsize>,
    start_index: usize,
    stables: &HashSet<Address>,
    is_second_round: bool
) -> Result<(usize, Vec<POOL>), DEXError> {
    let total = pools.len();
    if total == 0 {
        return Ok((0, vec![]));
    }
    
    let round_name = if is_second_round { "2nd round" } else { "1st round" };
    println!("Processing {} non-stable pools ({})", total, round_name);
    
    // 先预处理所有的请求，将pools分成两部分：有pre_pairs的和无pre_pairs的
    let mut all_requests = Vec::with_capacity(pools.len());
    let mut unchecked_pools = Vec::new();
    
    for pool in pools {
        let data = pool.data();
        let mut found_path = false;
        
        // 遍历pool中的每个token，寻找到stable的路径
        for (token_idx, token) in data.tokens.iter().enumerate() {
            let path = sm.index.get_path(&token.addr, stables, &sm.pools, None, None, false);

            // 如果找到路径，将所有池子作为pre_pair
            if !path.is_empty() {
                let pre_pairs: Vec<PoolReq> = path.iter()
                    .rev()  // 反转整个路径顺序
                    .map(|pool_index|
                        PoolReq {
                        addr: pool_index.addr,
                        version: U256::from(sm.pools.data.get(&pool_index.addr).unwrap().data().ver),
                        fee: U256::ZERO,
                        fp: sm.pools.data.get(&pool_index.addr).unwrap().data().fp,
                        inIndex: U256::from(pool_index.out_index),  // 反转 index
                        outIndex: U256::from(pool_index.in_index),  // 反转 index
                    })
                    .collect();
                
                // 创建当前池子的请求
                let pool_req = ViraLogic::CheckPairFeeInputDesc {
                    prePair: pre_pairs,
                    pair: vec![PoolReq {
                        addr: data.addr,
                        version: U256::from(data.ver),
                        fee: U_ZERO,
                        fp: data.fp,
                        inIndex: U256::from(token_idx),
                        outIndex: U_ZERO,
                    }],
                };
                
                all_requests.push((pool.clone(), pool_req));
                found_path = true;
                break;
            }
        }
        
        // 如果没有找到路径，直接将池子加入到unchecked_pools
        if !found_path {
            unchecked_pools.push(pool);
        }
    }
    
    println!("Found {} pools with path to stables, {} pools without path", 
        all_requests.len(), unchecked_pools.len());
    
    // 如果没有可处理的请求，直接返回
    if all_requests.is_empty() {
        return Ok((0, unchecked_pools));
    }
    
    // 重置处理计数器，确保百分比计算正确
    processed_counter.store(0, Ordering::SeqCst);
    let requests_total = all_requests.len();
    
    // 将请求分成更小的批次进行并发处理
    // 使用iter和buffer_unordered替代FuturesUnordered
    let futures = iter(all_requests.chunks(BATCH_SIZE).enumerate().map(|(batch_idx, batch)| {
        let mut valid_reqs = Vec::new();
        let mut pool_mapping = Vec::new();
        
        // 只处理有效的请求
        for (pool, req) in batch {
            valid_reqs.push(req.clone());
            pool_mapping.push(pool.clone());
        }

        let contract_clone = contract.clone();
        let processed_counter_clone = processed_counter.clone();
        let batch_start_idx = start_index + batch_idx * BATCH_SIZE;
        
        async move {
            match contract_clone.batch_check_pair_fee(valid_reqs).await {
                Ok(fees) => {
                    let current = processed_counter_clone.fetch_add(pool_mapping.len(), Ordering::SeqCst);
                    eprint!("\r处理非稳定币池子进度 ({}): {}/{} ({:.2}%)", 
                        round_name,
                        current + pool_mapping.len(), 
                        requests_total,
                        ((current + pool_mapping.len()) as f64 / requests_total as f64) * 100.0
                    );
                    
                    Ok((pool_mapping, fees, batch_start_idx))
                },
                Err(e) => {
                    panic!("{}", format!("Error checking fees for pools batch: {:?}", e).red());
                }
            }
        }
    }))
    .buffer_unordered(MAX_CONCURRENT_TASKS);
    
    let mut processed_count = 0;
    let mut processed_pools = Vec::new();
    
    // 收集并处理结果
    futures::pin_mut!(futures);
    while let Some(result) = futures.next().await {
        match result {
            Ok((pool_mapping, fees, batch_start_idx)) => {
                if !fees.is_empty() {
                    let mut pools_to_process = pool_mapping.clone();
                    process_fee_results(&mut pools_to_process, &fees, sm, batch_start_idx);
                    processed_count += fees.len();
                    processed_pools.extend(pool_mapping);
                }
            },
            Err(e) => {
                println!("\n{}", format!("Error processing non-stable pools batch: {:?}", e).red());
                return Err(e);
            }
        }
    }
    
    println!("\n非稳定币池子处理完成: 总共处理 {}/{} ({:.2}%)",
        processed_count, 
        requests_total,
        (processed_count as f64 / requests_total as f64) * 100.0
    );
    
    // 直接返回预先分离的未检查池子
    Ok((processed_count, unchecked_pools))
}

// 处理fee结果的辅助函数
fn process_fee_results(
    chunk: &mut [POOL], 
    fees: &[Vec<alloy::primitives::U256>], 
    sm: &mut StatusManager,
    start_index: usize
) {
    // 批量收集需要添加到 StatusManager 的池子
    let mut pools_to_add = Vec::new();

    for (i, (pool, fee)) in chunk.iter_mut().zip(fees).enumerate() {
        let data = pool.data_mut();
        
        // 先设置所有token的fee值
        let mut has_honey = false;
        for (token, &val) in data.tokens.iter_mut().zip(fee.iter()) {
            // 检查是否是蜜罐币
            if val == U_666666 {
                has_honey = true;
                token.fee = U_ZERO; // 蜜罐币的fee设为0
            } else {
                token.fee = val;
            }
        }

        // 默认状态为Good
        let mut current_status = Status::Good;
        
        // 按照优先级判断状态
        // 1. 检查是否存在900004 (通缩币)
        if data.tokens.iter().any(|t| t.fee == U_900004) {
            current_status = Status::OddBad;
        }
        // 2. 检查是否存在900005 (未知状态)
        else if data.tokens.iter().any(|t| t.fee == U_900005) {
            current_status = Status::Unknown;
        }
        // 3. 检查是否有fee大于2000
        else if data.tokens.iter().any(|t| t.fee > U_2000) {
            current_status = Status::Bad;
        }
        // 4. 检查是否有fee大于0小于2000
        else if data.tokens.iter().any(|t| t.fee > U_ZERO && t.fee <= U_2000) {
            current_status = Status::Fee;
        }
        // 5. 检查是否存在666666 (Honey pot)
        else if has_honey {
            current_status = Status::OddGood;
        }
        // 6. 其余情况保持Good状态
        
        // 设置状态
        data.status = current_status;

        // 打印结果
        let fee_info = data.tokens.iter()
            .map(|token| {
                if token.fee == U_ZERO {
                    token.symbol.to_string()
                } else {
                    format!("{} ({})", token.symbol, token.fee)
                }
            })
            .collect::<Vec<_>>()
            .join(" - ");

        let line = format!("({}) {} {}", start_index + i, data.addr, fee_info);
        let colored_line = match data.status {
            Status::Fee => line.green().to_string(),
            Status::Bad => line.red().to_string(),
            Status::OddGood => line.yellow().to_string(),
            Status::OddBad => line.red().bold().to_string(),
            Status::Unknown => line.bright_purple().to_string(),
            Status::NoStable => line.black().to_string(),
            Status::LowValue => line.bright_black().to_string(),
            _ => line
        };
        println!("{}", colored_line);

        // 将有效的pool添加到StatusManager中
        if data.status == Status::Good || data.status == Status::OddGood {
            pools_to_add.push(pool.clone());
        }
    }

    // 批量添加到 StatusManager
    for pool in pools_to_add {
        sm.add(pool);
    }
}


/// 检查MEV路径的费用和状态
///
/// 该函数实现MEV路径的批量检查，优化了逻辑结构和可读性
///
/// 核心逻辑：
/// 1. 统计并收集所有需要检查的MEV路径
/// 2. 按批次大小分组进行合约调用
/// 3. 处理返回结果并更新MEV状态
/// 4. 实现错误处理和重试机制
///
/// # 参数
/// * `contract` - 合约实例，用于调用batch_check_mev
/// * `pools` - 池子数据，包含MEV路径信息
///
/// # 返回值
/// * `Result<(), DEXError>` - 成功时返回Ok，失败时返回错误
pub async fn check_mevs(contract: &Contract, pools: &Pools) -> Result<(), DEXError> {
    println!("{}", "\n开始检查MEV路径...".green());

    // 收集所有需要检查的MEV路径索引
    let unchecked_mev_indices = collect_unchecked_mev_indices(pools);

    if unchecked_mev_indices.is_empty() {
        println!("没有需要检查的MEV路径");
        return Ok(());
    }

    let total_mevs = unchecked_mev_indices.len();
    println!("找到 {} 个MEV路径需要检查", total_mevs);

    // 按批次处理MEV路径
    let mut processed_count = 0;

    // 将MEV路径分批处理
    for batch in unchecked_mev_indices.chunks(MEV_BATCH_SIZE) {
        match process_mev_batch(contract, batch, pools).await {
            Ok(success_count) => {
                processed_count += success_count;
                print_progress(processed_count, total_mevs);
            }
            Err(e) => {
                println!("批量处理MEV失败: {:?}", e);
                // 继续处理下一批，不中断整个流程
            }
        }
    }

    println!("{}", format!("MEV路径检查完成！总共处理了 {} 个MEV路径", processed_count).green());
    Ok(())
}

/// 收集所有需要检查的MEV路径索引
///
/// 遍历所有池子的MEV列表，找出状态为Unchecked的MEV路径
///
/// # 参数
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Vec<(Address, usize)>` - 需要检查的MEV路径索引列表 (pool_addr, mev_index)
fn collect_unchecked_mev_indices(pools: &Pools) -> Vec<(Address, usize)> {
    let mut unchecked_indices = Vec::new();

    for entry in pools.mevs.iter() {
        let pool_addr = *entry.key();
        let mev_list = entry.value();

        for (mev_index, mev) in mev_list.iter().enumerate() {
            if mev.status0 == MevStatus::Unchecked {
                unchecked_indices.push((pool_addr, mev_index));
            }
        }
    }

    unchecked_indices
}

/// 打印处理进度
///
/// # 参数
/// * `processed` - 已处理数量
/// * `total` - 总数量
fn print_progress(processed: usize, total: usize) {
    let percentage = (processed as f64 / total as f64) * 100.0;
    println!("已处理 {}/{} 个MEV路径 ({:.1}%)", processed, total, percentage);
}

/// 处理一批MEV路径的检查
///
/// 将MEV路径转换为合约请求格式，调用合约进行批量检查，并处理结果
///
/// # 参数
/// * `contract` - 合约实例
/// * `mev_indices` - MEV路径的索引数组 (pool_addr, mev_index)
/// * `pools` - 池子数据，用于获取池子详细信息
///
/// # 返回值
/// * `Result<usize, DEXError>` - 成功处理的MEV数量
async fn process_mev_batch(
    contract: &Contract,
    mev_indices: &[(Address, usize)],
    pools: &Pools,
) -> Result<usize, DEXError> {
    // 构建合约请求数据
    let pool_reqs_batch = build_mev_batch_requests(mev_indices, pools);

    if pool_reqs_batch.is_empty() {
        println!("警告：没有有效的MEV路径数据可供检查");
        return Ok(0);
    }

    println!("正在检查 {} 个MEV路径...", mev_indices.len());

    // 尝试批量检查，失败时进行单个重试
    let check_results = execute_batch_check_with_retry(contract, pool_reqs_batch, mev_indices, pools).await?;

    // 处理检查结果并更新MEV状态
    update_mev_status_from_results(mev_indices, check_results, pools).await;

    Ok(mev_indices.len())
}

/// 构建MEV批量检查的合约请求数据
///
/// 将MEV路径转换为合约可识别的PoolReq格式
///
/// # 参数
/// * `mev_indices` - MEV路径索引数组
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Vec<Vec<ViraData::PoolReq>>` - 合约请求数据批次
fn build_mev_batch_requests(
    mev_indices: &[(Address, usize)],
    pools: &Pools
) -> Vec<Vec<ViraData::PoolReq>> {
    let mut pool_reqs_batch = Vec::with_capacity(mev_indices.len());

    for &(pool_addr, mev_index) in mev_indices {
        if let Some(pool_reqs) = build_single_mev_request(pool_addr, mev_index, pools) {
            pool_reqs_batch.push(pool_reqs);
        }
    }

    pool_reqs_batch
}

/// 构建单个MEV路径的合约请求数据
///
/// # 参数
/// * `pool_addr` - 池子地址
/// * `mev_index` - MEV索引
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Option<Vec<ViraData::PoolReq>>` - 单个MEV的合约请求数据，如果构建失败返回None
fn build_single_mev_request(
    pool_addr: Address,
    mev_index: usize,
    pools: &Pools
) -> Option<Vec<ViraData::PoolReq>> {
    let mev_list = pools.mevs.get(&pool_addr)?;
    let mev = mev_list.get(mev_index)?;

    let mut pool_reqs = Vec::with_capacity(mev.pools.len());

    for mev_pool in &mev.pools {
        let pool = pools.data.get(&mev_pool.addr)?;
        let pool_data = pool.data();

        let pool_req = ViraData::PoolReq {
            addr: mev_pool.addr,
            version: U256::from(pool_data.ver),
            fee: U256::ZERO,  // 费用在合约中计算
            fp: pool_data.fp,
            inIndex: U256::from(mev_pool.in_index),
            outIndex: U256::from(mev_pool.out_index),
        };

        pool_reqs.push(pool_req);
    }

    Some(pool_reqs)
}

/// 执行批量检查，失败时自动重试
///
/// 首先尝试批量检查，如果失败则等待后进行单个重试
///
/// # 参数
/// * `contract` - 合约实例
/// * `pool_reqs_batch` - 合约请求数据
/// * `mev_indices` - MEV路径索引
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>` - 检查结果
async fn execute_batch_check_with_retry(
    contract: &Contract,
    pool_reqs_batch: Vec<Vec<ViraData::PoolReq>>,
    mev_indices: &[(Address, usize)],
    pools: &Pools,
) -> Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError> {
    // 尝试批量检查
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        tokio::runtime::Handle::current().block_on(contract.batch_check_mev(pool_reqs_batch))
    })) {
        Ok(results) => {
            println!("批量检查成功，收到 {} 个结果", results.len());
            Ok(results)
        }
        Err(_) => {
            println!("{}批量合约调用失败，开始单个重试...", "⚠️ ".yellow());

            // 等待2秒后重试
            tokio::time::sleep(tokio::time::Duration::from_secs(2)).await;

            // 进行单个重试并收集结果
            retry_individual_mevs_with_results(contract, mev_indices, pools).await
        }
    }
}

/// 更新MEV状态基于检查结果
///
/// 根据合约返回的结果更新对应MEV的状态和gas信息
///
/// # 参数
/// * `mev_indices` - MEV路径索引数组
/// * `results` - 合约检查结果
/// * `pools` - 池子数据
async fn update_mev_status_from_results(
    mev_indices: &[(Address, usize)],
    results: Vec<ViraLogic::CheckMevsResultDesc>,
    pools: &Pools,
) {
    for (i, result) in results.iter().enumerate() {
        if i >= mev_indices.len() {
            break;
        }

        let (pool_addr, mev_index) = mev_indices[i];

        // 更新对应池子的MEV状态
        if let Some(mut mev_list) = pools.mevs.get_mut(&pool_addr) {
            if let Some(mev) = mev_list.get_mut(mev_index) {
                // 更新gas消耗
                mev.gas = result.gas;

                // 更新正向交易状态 (status0)
                mev.status0 = if !result.fee0.is_empty() {
                    MevStatus::Active
                } else {
                    MevStatus::Bad
                };

                // 更新反向交易状态 (status1)
                mev.status1 = if !result.fee1.is_empty() {
                    MevStatus::Active
                } else {
                    MevStatus::Bad
                };
            }
        }
    }
}

/// 单个重试MEV检查并返回结果
///
/// 当批量检查失败时，逐个重试每个MEV路径的检查
///
/// # 参数
/// * `contract` - 合约实例
/// * `mev_indices` - 待重试的MEV路径索引数组
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError>` - 重试结果
async fn retry_individual_mevs_with_results(
    contract: &Contract,
    mev_indices: &[(Address, usize)],
    pools: &Pools,
) -> Result<Vec<ViraLogic::CheckMevsResultDesc>, DEXError> {
    let mut all_results = Vec::new();

    println!("批量检查失败，开始逐个重试 {} 个MEV路径...", mev_indices.len());

    for &(pool_addr, mev_index) in mev_indices {
        match retry_single_mev(contract, pool_addr, mev_index, pools).await {
            Some(result) => all_results.push(result),
            None => {
                // 为失败的MEV创建一个空结果
                let empty_result = ViraLogic::CheckMevsResultDesc {
                    gas: U256::ZERO,
                    fee0: vec![],
                    fee1: vec![],
                };
                all_results.push(empty_result);
            }
        }
    }

    println!("单个重试完成，成功处理 {} 个MEV", all_results.len());
    Ok(all_results)
}

/// 重试单个MEV路径的检查
///
/// # 参数
/// * `contract` - 合约实例
/// * `pool_addr` - 池子地址
/// * `mev_index` - MEV索引
/// * `pools` - 池子数据
///
/// # 返回值
/// * `Option<ViraLogic::CheckMevsResultDesc>` - 检查结果，失败时返回None
async fn retry_single_mev(
    contract: &Contract,
    pool_addr: Address,
    mev_index: usize,
    pools: &Pools,
) -> Option<ViraLogic::CheckMevsResultDesc> {
    // 构建单个MEV的请求数据
    let pool_reqs = build_single_mev_request(pool_addr, mev_index, pools)?;

    // 尝试单个MEV检查
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        tokio::runtime::Handle::current().block_on(contract.batch_check_mev(vec![pool_reqs]))
    })) {
        Ok(results) => {
            if !results.is_empty() {
                Some(results[0].clone())
            } else {
                println!("单个MEV检查返回空结果 (池子: {})", pool_addr);
                None
            }
        }
        Err(_) => {
            println!("单个MEV检查失败 (池子: {})", pool_addr);
            None
        }
    }
}



#[cfg(test)]
mod tests {
    use super::*;
    use crate::vira::status::mev::{Mev, MevPool};
    use crate::vira::pool::{PoolData, PoolDataToken};
    use std::str::FromStr;

    /// 创建测试用的池子数据
    fn create_test_pool(addr: &str, ver: u16) -> POOL {
        let mut pool_data = PoolData::default();
        pool_data.addr = Address::from_str(addr).unwrap();
        pool_data.ver = ver;
        pool_data.fp = U256::from(3000); // 0.3% fee

        // 添加两个测试token
        pool_data.tokens = vec![
            PoolDataToken {
                addr: Address::from_str("0x1111111111111111111111111111111111111111").unwrap(),
                index: 0,
                symbol: "TOKEN0".to_string(),
                decimal: 18,
                reserve: U256::from(1000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
            PoolDataToken {
                addr: Address::from_str("0x2222222222222222222222222222222222222222").unwrap(),
                index: 1,
                symbol: "TOKEN1".to_string(),
                decimal: 18,
                reserve: U256::from(2000000),
                fee: U256::ZERO,
                weight: U256::ZERO,
            },
        ];

        // 创建UniV2Pool实例
        use crate::vira::dex::uni_v2::pool::UniV2Pool;
        POOL::UniV2Pool(UniV2Pool { data: pool_data })
    }

    /// 创建测试用的MEV路径
    fn create_test_mev(pool_addrs: Vec<&str>) -> Mev {
        let mut mev = Mev::default();
        mev.s_in = Address::from_str("0x1111111111111111111111111111111111111111").unwrap();
        mev.s_out = Address::from_str("0x2222222222222222222222222222222222222222").unwrap();

        mev.pools = pool_addrs.iter().enumerate().map(|(_i, addr)| {
            MevPool {
                addr: Address::from_str(addr).unwrap(),
                in_index: 0,
                out_index: 1,
                fee0: U256::ZERO,
                fee1: U256::ZERO,
            }
        }).collect();

        mev
    }

    #[test]
    fn test_mev_data_structure() {
        // 测试MEV数据结构的创建和基本属性
        let mev = create_test_mev(vec![
            "0x3333333333333333333333333333333333333333",
            "0x4444444444444444444444444444444444444444"
        ]);

        assert_eq!(mev.pools.len(), 2);
        assert_eq!(mev.status0, MevStatus::Unchecked);
        assert_eq!(mev.status1, MevStatus::Unchecked);
        println!("✅ MEV数据结构测试通过");
    }

    #[test]
    fn test_pools_data_structure() {
        // 测试Pools数据结构
        let pools = Pools::default();

        // 添加测试池子
        let pool1 = create_test_pool("0x3333333333333333333333333333333333333333", 2);
        let pool2 = create_test_pool("0x4444444444444444444444444444444444444444", 2);

        pools.data.insert(pool1.addr(), pool1);
        pools.data.insert(pool2.addr(), pool2);

        // 添加测试MEV
        let mev = create_test_mev(vec![
            "0x3333333333333333333333333333333333333333",
            "0x4444444444444444444444444444444444444444"
        ]);

        let main_pool_addr = Address::from_str("0x5555555555555555555555555555555555555555").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev]);

        assert_eq!(pools.data.len(), 2);
        assert_eq!(pools.mevs.len(), 1);
        println!("✅ Pools数据结构测试通过");
    }

    /// 模拟check_mevs函数的数据准备逻辑
    #[test]
    fn test_mev_filtering_logic() {
        let pools = Pools::default();

        // 创建混合状态的MEV路径
        let mut mev1 = create_test_mev(vec!["0x3333333333333333333333333333333333333333"]);
        mev1.status0 = MevStatus::Unchecked; // 需要检查
        mev1.status1 = MevStatus::Active;    // 已检查

        let mut mev2 = create_test_mev(vec!["0x4444444444444444444444444444444444444444"]);
        mev2.status0 = MevStatus::Active;    // 已检查
        mev2.status1 = MevStatus::Active;    // 已检查

        let mut mev3 = create_test_mev(vec!["0x5555555555555555555555555555555555555555"]);
        mev3.status0 = MevStatus::Unchecked; // 需要检查
        mev3.status1 = MevStatus::Unchecked; // 需要检查

        let main_pool_addr = Address::from_str("0x6666666666666666666666666666666666666666").unwrap();
        pools.mevs.insert(main_pool_addr, vec![mev1, mev2, mev3]);

        // 模拟过滤逻辑
        let mut unchecked_count = 0;
        for entry in pools.mevs.iter() {
            let mev_list = entry.value();
            for mev in mev_list.iter() {
                if mev.status0 == MevStatus::Unchecked || mev.status1 == MevStatus::Unchecked {
                    unchecked_count += 1;
                }
            }
        }

        assert_eq!(unchecked_count, 2); // mev1 和 mev3 需要检查
        println!("✅ MEV过滤逻辑测试通过，找到 {} 个需要检查的MEV", unchecked_count);
    }
}
